const db = require('../config/database');
const fs = require('fs').promises;
const path = require('path');
const { uploadGameFileToS3, checkS3Configuration } = require('../services/s3Service');

/**
 * Clean up temporary files
 * @param {Array} filePaths - Array of file paths to delete
 */
const cleanupTempFiles = async (filePaths) => {
  if (!filePaths || filePaths.length === 0) {
    return;
  }
  
  console.log(`🧹 Cleaning up ${filePaths.length} temporary files...`);
  
  for (const filePath of filePaths) {
    try {
      await fs.unlink(filePath);
      console.log(`✅ Deleted temp file: ${filePath}`);
    } catch (error) {
      console.warn(`⚠️ Could not delete temp file ${filePath}:`, error.message);
    }
  }
};

/**
 * Clean up old temporary files (older than 1 hour)
 */
const cleanupOldTempFiles = async () => {
  try {
    const tempDir = path.join(__dirname, '../../uploads/temp');
    const files = await fs.readdir(tempDir);
    const oneHourAgo = Date.now() - (60 * 60 * 1000); // 1 hour in milliseconds
    
    let deletedCount = 0;
    
    for (const file of files) {
      const filePath = path.join(tempDir, file);
      
      try {
        const stats = await fs.stat(filePath);
        
        if (stats.isFile() && stats.mtime.getTime() < oneHourAgo) {
          await fs.unlink(filePath);
          deletedCount++;
        } else if (stats.isDirectory() && stats.mtime.getTime() < oneHourAgo) {
          // Remove old directories too
          await fs.rmdir(filePath, { recursive: true });
          deletedCount++;
        }
      } catch (error) {
        console.warn(`Could not clean up old temp file/dir ${filePath}:`, error.message);
      }
    }
    
    if (deletedCount > 0) {
      console.log(`🧹 Cleaned up ${deletedCount} old temporary files/directories`);
    }
  } catch (error) {
    console.error('Error cleaning up old temp files:', error);
  }
};

// Schedule cleanup of old temp files every hour
setInterval(cleanupOldTempFiles, 60 * 60 * 1000);

// Improved move file function to handle any file type
const moveFile = async (file, gameId, destSubfolder, newFilename = null) => {
  if (!file || !file.path) return null;
  
  // Create destination directory
  const destDir = path.join(__dirname, '../../uploads/games', gameId.toString(), destSubfolder);
  await fs.mkdir(destDir, { recursive: true });
  
  // Determine destination filename
  const filename = newFilename || file.filename;
  
  // Create destination path
  const destPath = path.join(destDir, filename);
  
  try {
    // Copy file from temp to permanent location
    await fs.copyFile(file.path, destPath);

    
    // Remove original temp file
    await fs.unlink(file.path).catch(err => {
      console.warn(`⚠️ Could not delete temporary file ${file.path}:`, err);
    });
    
    return `/uploads/games/${gameId}/${destSubfolder}/${filename}`;
  } catch (err) {
    console.error(`❌ Error moving file:`, err);
    throw err;
  }
};

/**
 * Upload a generic file
 */
exports.uploadFile = async (req, res) => {
  try {
    const uploadType = req.params.type;
    const userId = req.user.id;
    
    if (!req.file && !req.body.externalUrl) {
      return res.status(400).json({ message: 'No file uploaded and no external URL provided' });
    }
    
    // Process the file based on type
    switch (uploadType) {
      case 'cover':
      case 'card':
      case 'gif':
      case 'screenshot':
        // Handle image uploads to S3
        const fileBuffer = await fs.readFile(req.file.path);
        const s3Result = await uploadGameFileToS3(
          fileBuffer,
          req.file.originalname,
          req.file.mimetype,
          req.gameId || 'temp',
          uploadType
        );
        
        // Clean up temporary file
        await cleanupTempFiles([req.file.path]);
        
        return res.json({
          message: 'File uploaded successfully to S3',
          fileName: req.file.filename,
          filePath: s3Result.url,
          s3Key: s3Result.key,
          fileSize: req.file.size,
          fileType: req.file.mimetype
        });
      default:
        return res.status(400).json({ message: 'Invalid upload type' });
    }
  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({ message: 'Server error during upload', error: error.message });
  }
};

/**
 * Upload user avatar
 */
exports.uploadAvatar = async (req, res) => {
  try {
    const userId = req.user.id;
    
    if (!req.file) {
      return res.status(400).json({ message: 'No file uploaded' });
    }
    
    // Insert avatar record into database using Knex
    const filePath = `/uploads/avatars/${userId}/${req.file.filename}`;
    const [avatarResult] = await db('user_avatars').insert({
      user_id: userId,
      file_name: req.file.filename,
      file_path: filePath,
      file_size: req.file.size,
      is_active: true
    }).returning('id');

    // Extract the actual ID from the result object
    const avatarId = avatarResult.id || avatarResult;

    // Update user profile to use new avatar
    await db('users')
      .where('id', userId)
      .update({ profile_image: filePath });

    // Update previous avatars to be inactive
    await db('user_avatars')
      .where('user_id', userId)
      .where('id', '!=', avatarId)
      .update({ is_active: false });
    
    res.json({
      message: 'Avatar uploaded successfully',
      avatarPath: filePath
    });
  } catch (error) {
    console.error('Avatar upload error:', error);
    res.status(500).json({ message: 'Server error during avatar upload', error: error.message });
  }
};

/**
 * Upload game files
 */
exports.uploadGame = async (req, res) => {
  let tempFilesToCleanup = []; // Track all temp files for cleanup

  try {
    // Check S3 configuration first with detailed logging
    console.log('🔍 Checking S3 configuration...');
    console.log('Environment check:', {
      NODE_ENV: process.env.NODE_ENV,
      AWS_REGION: process.env.AWS_REGION ? 'SET' : 'NOT SET',
      AWS_ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID ? `${process.env.AWS_ACCESS_KEY_ID.substring(0, 4)}****` : 'NOT SET',
      AWS_SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY ? 'SET' : 'NOT SET',
      AWS_S3_BUCKET_NAME: process.env.AWS_S3_BUCKET_NAME || 'NOT SET',
      STORAGE_TYPE: process.env.STORAGE_TYPE || 'NOT SET'
    });

    const s3Ready = await checkS3Configuration();
    if (!s3Ready) {
      console.error('❌ S3 configuration check failed');
      return res.status(500).json({
        message: 'AWS S3 is not properly configured. Please check your environment variables.',
        debug: {
          bucketName: process.env.AWS_S3_BUCKET_NAME,
          region: process.env.AWS_REGION,
          hasAccessKey: !!process.env.AWS_ACCESS_KEY_ID,
          hasSecretKey: !!process.env.AWS_SECRET_ACCESS_KEY
        }
      });
    }

    console.log('✅ S3 configuration check passed');

    // Collect all temp file paths for cleanup
    if (req.files) {
      for (const [fieldName, files] of Object.entries(req.files)) {
        files.forEach(file => tempFilesToCleanup.push(file.path));
      }
    }

    const userId = req.user.userId || req.user.id;

    // Get user info using Knex
    const user = await db('users')
      .select('username')
      .where('id', userId)
      .first();

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    // Parse game data from request
    const gameData = JSON.parse(req.body.gameData);
    const gameFilesMetadata = JSON.parse(req.body.gameFilesMetadata || '[]');
    
    // Generate slug from title
    const generateSlug = (title) => {
      return title
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .replace(/-+/g, '-') // Replace multiple hyphens with single
        .trim() // Remove leading/trailing spaces
        .substring(0, 100); // Limit length
    };
    
    const baseSlug = generateSlug(gameData.title);
    let slug = baseSlug;
    let counter = 1;
    
    // Check if slug already exists and make it unique
    while (true) {
      const existingGame = await db('games').where('slug', slug).first();
      if (!existingGame) break;
      slug = `${baseSlug}-${counter}`;
      counter++;
    }
    
    // Check for external links or uploaded files
    const hasExternalLinks = gameFilesMetadata.some(file => file.isExternalLink);
    const hasFileUploads = req.files && Object.keys(req.files).some(key => key.startsWith('gameFile_'));

    if (!hasFileUploads && !hasExternalLinks) {
      return res.status(400).json({
        message: 'No game file uploaded and no external links provided'
      });
    }

    // Insert game record using Knex (matching actual schema)
    const [gameResult] = await db('games').insert({
      user_id: userId,
      title: gameData.title,
      description: gameData.description,
      genre: gameData.genre,
      tags: gameData.tags || '',

      // Web game configuration
      is_web_game: gameData.isWebGame || false,
      web_game_url: gameData.webGameUrl || null,
      web_game_type: gameData.webGameType || null,
      has_embedded_version: gameData.hasEmbeddedVersion || false,

      // Release information
      release_date: gameData.releaseDate ? new Date(gameData.releaseDate) : new Date(),

      // Legacy fields - set defaults for removed pricing
      price_model: 'free',
      price: 0,
      credit_price: 0,
      is_free: true,
      payment_enabled: false,
      status: 'published', // Use 'published' instead of 'pending' to match schema
      platform: 'web', // Default platform
      slug: slug
    }).returning('id');

    // Extract the actual ID from the result object
    const gameId = gameResult.id || gameResult;

    // Process game file uploads to S3
    if (req.files) {
      // Process each gameFile upload (could be multiple)
      for (const fieldName of Object.keys(req.files)) {
        if (fieldName.startsWith('gameFile_')) {
          const fileObj = req.files[fieldName][0]; // Get the first file from the array
          const fileIndex = parseInt(fieldName.split('_')[1] || '0', 10);
          const fileMetadata = gameFilesMetadata[fileIndex] || {};
          
          try {
            // Read file buffer and upload to S3
            const fileBuffer = await fs.readFile(fileObj.path);
            const s3Result = await uploadGameFileToS3(
              fileBuffer,
              fileObj.originalname,
              fileObj.mimetype,
              gameId,
              'game'
            );
            
            // Insert file record into database with S3 URL using Knex (matching actual schema)
            await db('game_files').insert({
              game_id: gameId,
              file_name: fileObj.originalname,
              file_path: s3Result.url, // S3 URL instead of local path
              file_size: fileObj.size,
              mime_type: fileObj.mimetype,
              file_type: fileMetadata.fileType || 'game',
              
              // New fields from migrations
              description: fileMetadata.description || '',
              requires_purchase: fileMetadata.isPremium || false,
              is_external_link: false, // This is an uploaded file, not external
              external_url: '',
              is_web_playable: fileMetadata.isWebPlayable || false,
              web_entry_point: fileMetadata.webEntryPoint || '',
              s3_key: s3Result.key, // Store S3 key for future operations
              uploaded_at: new Date(),
              
              // Legacy fields
              platform: 'web', // Default platform
              version: '1.0.0', // Default version
              is_main_file: true, // Mark as main file
              download_url: s3Result.url,
              notes: fileMetadata.description || ''
            });
            
            // Note: Temp file cleanup handled in finally block
            
          } catch (error) {
            console.error(`Error uploading game file ${fileIndex}:`, error);
            throw error;
          }
        }
      }
    }
    
    // Process external links if present
    if (hasExternalLinks) {
      for (const file of gameFilesMetadata.filter(f => f.isExternalLink)) {
        await db('game_files').insert({
          game_id: gameId,
          file_name: file.name || 'External Link',
          file_path: file.externalUrl, // Use external URL as file path
          file_size: 0, // No file size
          file_type: file.fileType || 'game',
          
          // New fields from migrations
          description: file.description || '',
          requires_purchase: file.isPremium || false,
          is_external_link: true, // This is an external link
          external_url: file.externalUrl,
          is_web_playable: file.isWebPlayable || false,
          web_entry_point: file.webEntryPoint || '',
          s3_key: null, // No S3 key for external links
          uploaded_at: new Date(),
          
          // Legacy fields
          platform: 'web', // Default platform
          version: '1.0.0', // Default version
          is_main_file: false,
          download_url: file.externalUrl,
          notes: file.description || ''
        });
      }
    }
    
    // Process image uploads to S3 (card, gif only)
    const imageTypes = {
      'cardImage': 'card',
      'gifAnimation': 'gif'
    };

    for (const [fieldName, imageType] of Object.entries(imageTypes)) {
      if (req.files && req.files[fieldName]) {
        const imageFile = req.files[fieldName][0];

        try {
          // Read file buffer and upload to S3
          const fileBuffer = await fs.readFile(imageFile.path);
          const s3Result = await uploadGameFileToS3(
            fileBuffer,
            imageFile.originalname,
            imageFile.mimetype,
            gameId,
            imageType
          );

          // Use simpler filename based on type
          const ext = path.extname(imageFile.originalname);
          let simpleName;

          if (imageType === 'card') simpleName = `card${ext}`;
          else if (imageType === 'gif') simpleName = 'hover.gif';

          // Insert image record with S3 URL using Knex (matching actual schema)
          await db('game_images').insert({
            game_id: gameId,
            image_type: imageType,
            file_name: simpleName,
            file_path: s3Result.url, // S3 URL instead of local path
            file_size: imageFile.size,
            mime_type: imageFile.mimetype,
            s3_key: s3Result.key, // Store S3 key for future operations
            display_order: 0
          });

          // Note: Temp file cleanup handled in finally block

        } catch (error) {
          console.error(`Error uploading ${imageType} image:`, error);
          throw error;
        }
      }
    }
    


    
    res.status(201).json({
      message: 'Game uploaded successfully to AWS S3',
      gameId: gameId,
      title: gameData.title,
      s3Enabled: true
    });

  } catch (error) {
    console.error('Game upload error:', error);

    res.status(500).json({
      message: 'Server error during game upload to S3',
      error: error.message
    });
  } finally {
    // Always clean up temporary files
    await cleanupTempFiles(tempFilesToCleanup);
  }
};
