import { useState, useRef, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { 
  FaCloudUploadAlt, FaImage, FaFileArchive, FaInfoCircle, FaLock, 
  FaGamepad, FaEye, FaCreditCard, FaChevronDown, FaChevronUp, 
  FaPlus, FaTrash, FaDownload, FaLockOpen, FaLink } from 'react-icons/fa';
import { useAuth } from '../context/AuthContext';
import GamePreviewPanel from '../components/GamePreviewPanel';
import GameCard from '../components/GameCard';

import { uploadGame } from '../services/uploadService';

const UploadGamePage = () => {
  const { user } = useAuth();
  const location = useLocation();
  const cardImageInputRef = useRef(null);
  const gifAnimationInputRef = useRef(null);

  // Preview panel state
  const [previewOpen, setPreviewOpen] = useState(false);
  const [cardPreviewOpen, setCardPreviewOpen] = useState(false);

  // Form state
  const [gameData, setGameData] = useState({
    title: '',
    description: '',
    genre: '',
    tags: '',

    // Web game fields
    isWebGame: false,
    webGameUrl: '',
    webGameType: 'html5',
    hasEmbeddedVersion: false,

    // Release date
    releaseDate: new Date().toISOString().split('T')[0] // YYYY-MM-DD format
  });

  // File upload states
  // Replace single gameFile with array of game files
  const [gameFiles, setGameFiles] = useState([]);
  const [cardImage, setCardImage] = useState(null);
  const [gifAnimation, setGifAnimation] = useState(null);
  const [cardImagePreview, setCardImagePreview] = useState(null);
  const [gifAnimationPreview, setGifAnimationPreview] = useState(null);

  // Validation and UI states
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [validationFailed, setValidationFailed] = useState(false);
  const errorSummaryRef = useRef(null);

  // Add useEffect to check if game has purchases (when editing an existing game)
  useEffect(() => {
    // This would normally check the game data from the server
    // For now we'll just simulate with a placeholder
    if (location.pathname.includes('/edit/') && gameData.id) {
      // You would make an API call here to check if the game has purchases
      checkGamePurchases(gameData.id);
    }
  }, [gameData.id, location.pathname]);
  
  // Function to check if a game has any purchases
  const checkGamePurchases = async () => {
    try {
      // This would be an API call in a real implementation
      // const result = await api.checkGamePurchases(gameId);
      // setGameHasPurchases(result.hasPurchases);
      
      // For demonstration, we'll just set it to false
      // setGameHasPurchases(false);
    } catch (error) {
      console.error('Error checking game purchases:', error);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    
    // Special handling for tags to limit to 3
    if (name === 'tags') {
      const tags = value.split(',').map(tag => tag.trim()).filter(tag => tag);
      
      if (tags.length > 3) {
        // If more than 3 tags, show error and limit to first 3
        setErrors({
          ...errors,
          tags: "Maximum 3 tags allowed"
        });
        // Only keep the first 3 tags
        const limitedTags = tags.slice(0, 3).join(', ');
        setGameData({
          ...gameData,
          tags: limitedTags
        });
        return;
      }
    }
    
    setGameData({
      ...gameData,
      [name]: value
    });

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null
      });
    }
  };



  // New function to handle game file uploads
  const handleGameFileChange = (e, index) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      
      // Update the gameFiles array at specific index
      const updatedGameFiles = [...gameFiles];
      
      if (updatedGameFiles[index]) {
        // Update existing file entry
        updatedGameFiles[index] = {
          ...updatedGameFiles[index],
          file: file,
          name: file.name
        };
      } else {
        // Create new file entry
        updatedGameFiles[index] = {
          file: file,
          name: file.name,
          description: '',
          isPremium: false,
          fileType: 'game', // Default type
          isExternalLink: false,
          externalUrl: '',
          isWebPlayable: false,
          webEntryPoint: 'index.html'
        };
      }
      
      setGameFiles(updatedGameFiles);
      
      // Clear error
      if (errors.gameFiles) {
        setErrors({
          ...errors,
          gameFiles: null
        });
      }
    }
  };

  // Function to add new empty file slot with external link option
  const handleAddGameFile = () => {
    if (gameFiles.length < 5) {
      setGameFiles([...gameFiles, {
        file: null,
        name: '',
        description: '',
        isPremium: false,
        fileType: 'game',
        isExternalLink: false,
        externalUrl: '',
        isWebPlayable: false,
        webEntryPoint: 'index.html'
      }]);
    }
  };

  // Function to remove game file from array
  const handleRemoveGameFile = (indexToRemove) => {
    setGameFiles(gameFiles.filter((_, index) => index !== indexToRemove));
  };

  const handleGameFileDescriptionChange = (e, index) => {
    const updatedGameFiles = [...gameFiles];
    updatedGameFiles[index].description = e.target.value;
    setGameFiles(updatedGameFiles);
  };

  const handleGameFileTypeChange = (e, index) => {
    const updatedGameFiles = [...gameFiles];
    updatedGameFiles[index].fileType = e.target.value;
    setGameFiles(updatedGameFiles);
  };

  const handleTogglePremium = (index) => {
    const updatedGameFiles = [...gameFiles];
    updatedGameFiles[index].isPremium = !updatedGameFiles[index].isPremium;
    setGameFiles(updatedGameFiles);
  };

  // New handlers for web playable files
  const handleToggleWebPlayable = (index) => {
    const updatedGameFiles = [...gameFiles];
    updatedGameFiles[index].isWebPlayable = !updatedGameFiles[index].isWebPlayable;
    
    // If enabling web playable, auto-enable hasEmbeddedVersion for the game
    if (updatedGameFiles[index].isWebPlayable) {
      setGameData({
        ...gameData,
        hasEmbeddedVersion: true,
        isWebGame: true
      });
    }
    
    setGameFiles(updatedGameFiles);
  };

  const handleWebEntryPointChange = (e, index) => {
    const updatedGameFiles = [...gameFiles];
    updatedGameFiles[index].webEntryPoint = e.target.value;
    setGameFiles(updatedGameFiles);
  };

  // Existing handlers updated
  const handleToggleFileSource = (index) => {
    const updatedGameFiles = [...gameFiles];
    
    // Toggle external link state
    updatedGameFiles[index].isExternalLink = !updatedGameFiles[index].isExternalLink;
    
    // If switching to external link, clear file and set default values
    if (updatedGameFiles[index].isExternalLink) {
      updatedGameFiles[index].file = null;
      updatedGameFiles[index].name = '';
      updatedGameFiles[index].externalUrl = '';
    } else {
      // If switching to file upload, clear external URL
      updatedGameFiles[index].externalUrl = '';
    }
    
    setGameFiles(updatedGameFiles);
  };

  const handleExternalUrlChange = (e, index) => {
    const updatedGameFiles = [...gameFiles];
    updatedGameFiles[index].externalUrl = e.target.value;
    setGameFiles(updatedGameFiles);
  };



  const handleCardImageChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setCardImage(file);

      // Preview
      const reader = new FileReader();
      reader.onload = (e) => setCardImagePreview(e.target.result);
      reader.readAsDataURL(file);

      setErrors({
        ...errors,
        cardImage: null
      });
    }
  };

  // Handle GIF animation upload
  const handleGifAnimationChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setGifAnimation(file);

      // Preview
      const reader = new FileReader();
      reader.onload = (e) => setGifAnimationPreview(e.target.result);
      reader.readAsDataURL(file);
    }
  };





  const validateForm = () => {
    const newErrors = {};

    // Title validation
    if (!gameData.title.trim()) {
      newErrors.title = "Game title is required";
    }

    // Description validation
    if (!gameData.description.trim()) {
      newErrors.description = "Game description is required";
    }

    // Genre validation
    if (!gameData.genre) {
      newErrors.genre = "Please select a genre";
    }

    // Tags validation - check if at least 1 tag exists
    const tags = gameData.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
    if (tags.length === 0) {
      newErrors.tags = "Please enter at least 1 tag";
    }

    // Game files validation - require at least one file OR valid URL
    if (gameFiles.length === 0 || !gameFiles.some(file => 
      (file.file !== null && !file.isExternalLink) || 
      (file.isExternalLink && file.externalUrl?.trim())
    )) {
      newErrors.gameFiles = "At least one game file or download link is required";
    }

    // Check external URLs format
    gameFiles.forEach((file, index) => {
      if (file.isExternalLink && file.externalUrl) {
        // Simple URL validation
        try {
          new URL(file.externalUrl);
        } catch {
          newErrors[`gameFileUrl_${index}`] = "Please enter a valid URL";
        }
      }
    });

    // Card image validation
    if (!cardImage) {
      newErrors.cardImage = "Card image is required";
    }

    // Make GIF animation required
    if (!gifAnimation) {
      newErrors.gifAnimation = "Hover animation GIF is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };



  const handleSubmit = async (e) => {
    e.preventDefault();

    // Scroll to top if there were previous errors
    if (validationFailed) {
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    if (!validateForm()) {
      setValidationFailed(true); // Set validation failed flag
      
      // Scroll to error summary
      setTimeout(() => {
        if (errorSummaryRef.current) {
          errorSummaryRef.current.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 100);
      
      return;
    }

    setValidationFailed(false); // Reset validation failed flag
    setIsSubmitting(true);

    try {
      // Call the uploadGame service function with modified parameters
      await uploadGame(
        gameData,
        gameFiles, // Now passing array of game files
        null, // coverImage removed
        cardImage,
        gifAnimation,
        [] // screenshots removed
      );
      
      
      setSubmitSuccess(true);
      
      // Reset form
      setGameData({
        title: '',
        description: '',
        genre: '',
        tags: '',

        // Web game fields
        isWebGame: false,
        webGameUrl: '',
        webGameType: 'html5',
        hasEmbeddedVersion: false,

        // Release date
        releaseDate: new Date().toISOString().split('T')[0] // YYYY-MM-DD format
      });
      setGameFiles([]); // Reset game files array
      setCardImage(null);
      setGifAnimation(null);
      setCardImagePreview(null);
      setGifAnimationPreview(null);

      // Reset file input references
      if (cardImageInputRef.current) cardImageInputRef.current.value = '';
      if (gifAnimationInputRef.current) gifAnimationInputRef.current.value = '';
      
      // Navigate to the game page after successful upload
      // Uncomment this when you're ready to implement it
      // navigate(`/game/${result.gameId}`);
      
    } catch (error) {
      console.error('Upload error:', error);
      
      setErrors({
        ...errors,
        submit: error.message || "An error occurred while uploading your game. Please try again."
      });
      
      // Scroll to the error
      window.scrollTo({ top: 0, behavior: 'smooth' });
      
    } finally {
      setIsSubmitting(false);
    }
  };

  // Helper function to count errors
  const countErrors = () => {
    return Object.keys(errors).length;
  };

  // Helper function to generate error message list
  const generateErrorList = () => {
    return Object.entries(errors).map(([field, message]) => {
      // Map field names to more readable forms
      const readableFieldNames = {
        title: "Game Title",
        description: "Game Description",
        genre: "Genre",
        tags: "Tags",
        gameFiles: "Game Files",
        cardImage: "Card Thumbnail Image",
        gifAnimation: "Hover Animation GIF"
      };

      const fieldName = readableFieldNames[field] || field;
      return { field, message, fieldName };
    });
  };

  // Toggle preview panel
  const togglePreview = () => {
    setPreviewOpen(!previewOpen);
  };

  // Close preview panel
  const closePreview = () => {
    setPreviewOpen(false);
  };
  
  // Toggle card preview
  const toggleCardPreview = () => {
    setCardPreviewOpen(!cardPreviewOpen);
  };
  
  // Create a game object for the GameCard preview
  const gameCardPreview = {
    id: 'preview',
    title: gameData.title || 'Your Game Title',
    image: cardImagePreview || 'https://via.placeholder.com/300x150?text=Game+Card+Image',
    genre: gameData.genre || 'Genre',
    paymentType: 'free',
    price: 'Free',
    tags: gameData.tags ? gameData.tags.split(',').map(tag => tag.trim()) : []
  };

  // Render different content based on authentication status
  if (!user) {
    return (
      <div className="min-h-screen bg-gray-900 text-white">
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-4xl mx-auto text-center">
            <FaGamepad className="text-orange-500 text-8xl mx-auto mb-8" />
            <h1 className="text-5xl font-bold text-white mb-4">Share Your Game with the World!</h1>
            <p className="text-xl text-gray-300 mb-12">Upload and distribute your indie games for FREE on IndieRepo</p>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
              <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <FaCloudUploadAlt className="text-blue-500 text-4xl mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-3">Free Uploads</h3>
                <p className="text-gray-300">Distribute your games without paying any platform fees</p>
              </div>

              <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <FaLock className="text-green-500 text-4xl mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-3">Monetization Options</h3>
                <p className="text-gray-300">Choose between free games, fixed pricing or our credit system</p>
              </div>

              <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <FaInfoCircle className="text-purple-500 text-4xl mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-3">Community Exposure</h3>
                <p className="text-gray-300">Get your game in front of passionate indie game players</p>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <Link to="/login" className="bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-200">
                Login to Upload Your Game
              </Link>
              <Link to="/register" className="bg-gray-700 hover:bg-gray-600 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-colors duration-200">
                Create an Account
              </Link>
            </div>

            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700 max-w-2xl mx-auto">
              <blockquote className="text-lg text-gray-300 italic">
                &ldquo;IndieRepo helped me share my passion project with players worldwide without any upfront costs!&rdquo;
                <footer className="text-orange-400 font-medium mt-2">- Indie Developer</footer>
              </blockquote>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // For logged-in users, show the upload form
  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <div className="container mx-auto px-4 py-8">
        <h1 className="flex items-center gap-3 text-4xl font-bold text-white mb-8">
          <FaCloudUploadAlt className="text-orange-500" /> Upload Your Game
        </h1>

        <button 
          className="fixed top-4 right-4 z-40 bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-lg shadow-lg transition-colors duration-200 flex items-center gap-2"
          onClick={togglePreview}
          title="Preview Game Page"
        >
          <FaEye /> {window.innerWidth > 992 ? "Preview Game Page" : ""}
        </button>

        {/* Card Preview Toggle Button */}
        <button 
          className="w-full bg-gray-800 hover:bg-gray-700 border border-gray-600 text-white p-4 rounded-lg mb-6 flex items-center justify-between transition-colors duration-200"
          onClick={toggleCardPreview}
        >
          <div className="flex items-center gap-2">
            <FaCreditCard className="text-orange-500" /> 
            {cardPreviewOpen ? "Hide Card Preview" : "Show Card Preview"}
          </div>
          {cardPreviewOpen ? <FaChevronUp className="text-orange-500" /> : <FaChevronDown className="text-orange-500" />}
        </button>

        {/* GameCard Preview - Only shown when toggled */}
        <div className={`transition-all duration-300 ease-in-out overflow-hidden ${cardPreviewOpen ? 'max-h-none opacity-100 mb-8' : 'max-h-0 opacity-0'}`}>
          <h3 className="text-xl font-semibold text-white mb-4">Design Your Game Card</h3>
          
          {/* Added card image selection inside the preview container */}
          <div className="space-y-4 mb-6">
            <p className="text-white font-medium">Card Thumbnail Image *</p>
            <div className="space-y-2">
              <input 
                type="file" 
                id="cardImage" 
                ref={cardImageInputRef}
                onChange={handleCardImageChange}
                accept=".png,.jpg,.jpeg"
                className="hidden"
              />
              <div 
                className={`border-2 border-dashed rounded-lg p-6 cursor-pointer transition-colors duration-200 ${
                  errors.cardImage ? 'border-red-500 bg-red-900 bg-opacity-20' : 
                  cardImage ? 'border-green-500 bg-green-900 bg-opacity-20' : 
                  'border-gray-600 bg-gray-800 hover:border-orange-500 hover:bg-gray-700'
                }`}
                onClick={() => cardImageInputRef.current.click()}
              >
                {cardImagePreview ? (
                  <img src={cardImagePreview} alt="Card preview" className="w-full h-32 object-cover rounded-lg" />
                ) : (
                  <div className="text-center">
                    <FaImage className="text-gray-400 text-4xl mx-auto mb-2" />
                    <span className="text-gray-300">Click to select a card image</span>
                  </div>
                )}
              </div>
            </div>
            {errors.cardImage && <div className="text-red-400 text-sm">{errors.cardImage}</div>}
            <div className="text-gray-500 text-sm">Recommended size: 280x158 px (16:9 ratio)</div>
            
            {/* Add GIF Animation upload option - now required with * */}
            <div className="space-y-2">
              <p className="text-white font-medium">Hover Animation (GIF) *</p>
              <div className="space-y-2">
                <input 
                  type="file" 
                  id="gifAnimation" 
                  ref={gifAnimationInputRef}
                  onChange={handleGifAnimationChange}
                  accept=".gif"
                  className="hidden"
                />
                <div 
                  className={`border-2 border-dashed rounded-lg p-6 cursor-pointer transition-colors duration-200 ${
                    errors.gifAnimation ? 'border-red-500 bg-red-900 bg-opacity-20' : 
                    gifAnimation ? 'border-green-500 bg-green-900 bg-opacity-20' : 
                    'border-gray-600 bg-gray-800 hover:border-orange-500 hover:bg-gray-700'
                  }`}
                  onClick={() => gifAnimationInputRef.current.click()}
                >
                  {gifAnimationPreview ? (
                    <img src={gifAnimationPreview} alt="GIF preview" className="w-full h-32 object-cover rounded-lg" />
                  ) : (
                    <div className="text-center">
                      <FaImage className="text-gray-400 text-4xl mx-auto mb-2" />
                      <span className="text-gray-300">Click to select a GIF for hover animation</span>
                    </div>
                  )}
                </div>
              </div>
              {errors.gifAnimation && <div className="text-red-400 text-sm">{errors.gifAnimation}</div>}
              <div className="text-gray-500 text-sm">Recommended size: 280x270px - Will replace card on hover</div>
            </div>
          </div>
        
          <div className="flex justify-center mb-4">
            <div className="relative">
              {gifAnimationPreview && <img src={gifAnimationPreview} alt="Hover animation" className="absolute inset-0 w-full h-full object-cover rounded-lg opacity-0 hover:opacity-100 transition-opacity duration-300 z-10" />}
              <GameCard game={gameCardPreview} />
            </div>
          </div>
          <p className="text-center text-gray-400 text-sm">
            {gifAnimationPreview 
              ? "Hover over the card to see your animation" 
              : "This is how your game will appear in listings"}
          </p>
        </div>

        {submitSuccess ? (
          <div className="bg-green-900 bg-opacity-50 border border-green-600 rounded-lg p-8 text-center">
            <h2 className="text-2xl font-bold text-green-200 mb-4">Game Uploaded Successfully!</h2>
            <p className="text-green-300 mb-6">Your game has been submitted and will be available shortly after review.</p>
            <button 
              className="bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-200" 
              onClick={() => setSubmitSuccess(false)}
            >
              Upload Another Game
            </button>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Error summary - only visible when validation fails */}
            {validationFailed && (
              <div className="bg-red-900 bg-opacity-50 border border-red-600 rounded-lg p-6" ref={errorSummaryRef}>
                <h3 className="flex items-center gap-2 text-red-200 text-lg font-semibold mb-3">
                  <FaInfoCircle className="text-red-400" /> Please complete all required fields
                </h3>
                <p className="text-red-300 mb-4">The following {countErrors()} {countErrors() === 1 ? 'field' : 'fields'} need attention:</p>
                <ul className="list-disc list-inside space-y-1 text-red-300 mb-4">
                  {generateErrorList().map((error, index) => (
                    <li key={index}>{error.fieldName} - {error.message}</li>
                  ))}
                </ul>
                <p className="text-red-400 text-sm">Required fields are marked with an asterisk (*)</p>
              </div>
            )}

            {/* New Card Image Section - Add a specific class to ensure it stays hidden */}
            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700 hidden">
              <h2 className="text-2xl font-bold text-white mb-4">Card Image</h2>
              <p className="text-gray-300 mb-6">
                This image will be used as the thumbnail in game listings. Choose an attention-grabbing image that represents your game well at a small size.
              </p>

              <div className="space-y-4">
                <label htmlFor="cardImage" className="block text-white font-medium">Card Thumbnail Image *</label>
                <div className="space-y-2">
                  <input 
                    type="file" 
                    id="cardImage" 
                    ref={cardImageInputRef}
                    onChange={handleCardImageChange}
                    accept=".png,.jpg,.jpeg"
                    className="hidden"
                  />
                  <div 
                    className={`border-2 border-dashed rounded-lg p-6 cursor-pointer transition-colors duration-200 ${
                      errors.cardImage ? 'border-red-500 bg-red-900 bg-opacity-20' : 
                      cardImage ? 'border-green-500 bg-green-900 bg-opacity-20' : 
                      'border-gray-600 bg-gray-700 hover:border-orange-500 hover:bg-gray-600'
                    }`}
                    onClick={() => cardImageInputRef.current.click()}
                  >
                    {cardImagePreview ? (
                      <img src={cardImagePreview} alt="Card preview" className="w-full h-32 object-cover rounded-lg" />
                    ) : (
                      <div className="text-center">
                        <FaImage className="text-gray-400 text-4xl mx-auto mb-2" />
                        <span className="text-gray-300">Click to select a card image</span>
                      </div>
                    )}
                  </div>
                </div>
                {errors.cardImage && <div className="text-red-400 text-sm">{errors.cardImage}</div>}
                <div className="text-gray-500 text-sm">Recommended size: 280x158 px (16:9 ratio) - PNG or JPG only</div>
              </div>

              {/* Add GIF Animation upload option - now required with * */}
              <div className="space-y-4 mt-6">
                <label htmlFor="gifAnimation" className="block text-white font-medium">Hover Animation (GIF) *</label>
                <div className="space-y-2">
                  <input 
                    type="file" 
                    id="gifAnimation" 
                    ref={gifAnimationInputRef}
                    onChange={handleGifAnimationChange}
                    accept=".gif"
                    className="hidden"
                  />
                  <div 
                    className={`border-2 border-dashed rounded-lg p-6 cursor-pointer transition-colors duration-200 ${
                      errors.gifAnimation ? 'border-red-500 bg-red-900 bg-opacity-20' : 
                      gifAnimation ? 'border-green-500 bg-green-900 bg-opacity-20' : 
                      'border-gray-600 bg-gray-700 hover:border-orange-500 hover:bg-gray-600'
                    }`}
                    onClick={() => gifAnimationInputRef.current.click()}
                  >
                    {gifAnimationPreview ? (
                      <img src={gifAnimationPreview} alt="GIF preview" className="w-full h-32 object-cover rounded-lg" />
                    ) : (
                      <div className="text-center">
                        <FaImage className="text-gray-400 text-4xl mx-auto mb-2" />
                        <span className="text-gray-300">Add a GIF for hover animation</span>
                      </div>
                    )}
                  </div>
                </div>
                {errors.gifAnimation && <div className="text-red-400 text-sm">{errors.gifAnimation}</div>}
                <div className="text-gray-500 text-sm">Recommended size: 280x270px - Will replace card on hover</div>
              </div>
            </div>

            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <h2 className="text-2xl font-bold text-white mb-6">Game Information</h2>

              <div className="space-y-6">
                <div>
                  <label htmlFor="title" className="block text-white font-medium mb-2">Game Title *</label>
                  <input 
                    type="text" 
                    id="title" 
                    name="title" 
                    value={gameData.title}
                    onChange={handleInputChange}
                    className={`w-full bg-gray-700 border rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                      errors.title ? 'border-red-500' : 'border-gray-600'
                    }`}
                    placeholder="Enter your game title"
                  />
                  {errors.title && <div className="text-red-400 text-sm mt-1">{errors.title}</div>}
                </div>

                <div>
                  <label htmlFor="description" className="block text-white font-medium mb-2">Game Description *</label>
                  <textarea 
                    id="description" 
                    name="description" 
                    value={gameData.description}
                    onChange={handleInputChange}
                    rows="6"
                    className={`w-full bg-gray-700 border rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent resize-vertical ${
                      errors.description ? 'border-red-500' : 'border-gray-600'
                    }`}
                    placeholder="Describe your game..."
                  ></textarea>
                  {errors.description && <div className="text-red-400 text-sm mt-1">{errors.description}</div>}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="genre" className="block text-white font-medium mb-2">Genre *</label>
                    <select 
                      id="genre" 
                      name="genre" 
                      value={gameData.genre}
                      onChange={handleInputChange}
                      className={`w-full bg-gray-700 border rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                        errors.genre ? 'border-red-500' : 'border-gray-600'
                      }`}
                    >
                      <option value="">Select a genre</option>
                      <option value="action">Action</option>
                      <option value="adventure">Adventure</option>
                      <option value="rpg">RPG</option>
                      <option value="simulation">Simulation</option>
                      <option value="strategy">Strategy</option>
                      <option value="sports">Sports</option>
                      <option value="puzzle">Puzzle</option>
                      <option value="horror">Horror</option>
                      <option value="platformer">Platformer</option>
                      <option value="shooter">Shooter</option>
                    </select>
                    {errors.genre && <div className="text-red-400 text-sm mt-1">{errors.genre}</div>}
                  </div>

                  <div>
                    <label htmlFor="tags" className="block text-white font-medium mb-2">Tags (max 3, comma separated) *</label>
                    <input 
                      type="text" 
                      id="tags" 
                      name="tags" 
                      value={gameData.tags}
                      onChange={handleInputChange}
                      placeholder="e.g. 2D, Pixel Art, Roguelike"
                      className={`w-full bg-gray-700 border rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                        errors.tags ? 'border-red-500' : 'border-gray-600'
                      }`}
                    />
                    {errors.tags && <div className="text-red-400 text-sm mt-1">{errors.tags}</div>}
                    <div className="text-gray-500 text-sm mt-1">Enter 1-3 tags that describe your game</div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <h2 className="text-2xl font-bold text-white mb-4">Game Files</h2>
              <p className="text-gray-300 mb-6">
                Upload up to 5 game files. You can mark specific files as premium content that will 
                only be accessible after purchase.
              </p>

              {errors.gameFiles && <div className="bg-red-900 bg-opacity-50 border border-red-600 text-red-200 p-3 rounded-lg mb-4">{errors.gameFiles}</div>}

              <div className="space-y-6">
                {gameFiles.map((gameFileItem, index) => (
                  <div key={index} className="bg-gray-700 rounded-lg p-6 border border-gray-600">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-white">File {index + 1}</h3>
                      <button 
                        type="button" 
                        className="flex items-center gap-2 bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded-lg transition-colors duration-200"
                        onClick={() => handleRemoveGameFile(index)}
                      >
                        <FaTrash className="text-sm" /> Remove
                      </button>
                    </div>
                    
                    {/* Add toggle between file upload and external link */}
                    <div className="flex gap-2 mb-4">
                      <button 
                        type="button"
                        className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
                          !gameFileItem.isExternalLink 
                            ? 'bg-orange-500 text-white' 
                            : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
                        }`}
                        onClick={() => handleToggleFileSource(index)}
                      >
                        <FaFileArchive /> Upload File
                      </button>
                      <button 
                        type="button"
                        className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
                          gameFileItem.isExternalLink 
                            ? 'bg-orange-500 text-white' 
                            : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
                        }`}
                        onClick={() => handleToggleFileSource(index)}
                      >
                        <FaLink /> External Link
                      </button>
                    </div>
                    
                    {/* Show file upload or external link input based on selection */}
                    {gameFileItem.isExternalLink ? (
                      <div className="space-y-4">
                        <div>
                          <label htmlFor={`externalUrl-${index}`} className="block text-white font-medium mb-2">Download Link URL</label>
                          <input 
                            type="text" 
                            id={`externalUrl-${index}`}
                            value={gameFileItem.externalUrl || ''}
                            onChange={(e) => handleExternalUrlChange(e, index)}
                            placeholder="https://drive.google.com/... or https://dropbox.com/..."
                            className={`w-full bg-gray-600 border rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent ${
                              errors[`gameFileUrl_${index}`] ? 'border-red-500' : 'border-gray-500'
                            }`}
                          />
                          {errors[`gameFileUrl_${index}`] && (
                            <div className="text-red-400 text-sm mt-1">{errors[`gameFileUrl_${index}`]}</div>
                          )}
                          <div className="text-gray-400 text-sm mt-1">
                            Provide a direct download link from Dropbox, Google Drive, or any other file hosting service.
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <input 
                          type="file" 
                          id={`gameFile-${index}`} 
                          onChange={(e) => handleGameFileChange(e, index)}
                          accept=".zip,.rar,.7z,.exe,.dmg,.app"
                          className="hidden"
                        />
                        <div 
                          className={`border-2 border-dashed rounded-lg p-6 cursor-pointer transition-colors duration-200 ${
                            gameFileItem.file 
                              ? 'border-green-500 bg-green-900 bg-opacity-20' 
                              : 'border-gray-500 bg-gray-600 hover:border-orange-500 hover:bg-gray-500'
                          }`}
                          onClick={() => document.getElementById(`gameFile-${index}`).click()}
                        >
                          <div className="text-center">
                            <FaFileArchive className="text-gray-300 text-4xl mx-auto mb-2" />
                            <span className="text-gray-200">{gameFileItem.file ? gameFileItem.name : 'Click to select a file'}</span>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    <div className="space-y-4 mt-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label htmlFor={`fileType-${index}`} className="block text-white font-medium mb-2">File Type</label>
                          <select 
                            id={`fileType-${index}`}
                            value={gameFileItem.fileType}
                            onChange={(e) => handleGameFileTypeChange(e, index)}
                            className="w-full bg-gray-600 border border-gray-500 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                          >
                            <option value="game">Full Game</option>
                            <option value="demo">Demo</option>
                            <option value="mod">Mod</option>
                            <option value="tools">Tools</option>
                            <option value="other">Other</option>
                          </select>
                        </div>
                        
                        <div>
                          <label htmlFor={`fileDescription-${index}`} className="block text-white font-medium mb-2">Description</label>
                          <input 
                            type="text" 
                            id={`fileDescription-${index}`}
                            placeholder="Briefly describe what this file contains"
                            value={gameFileItem.description}
                            onChange={(e) => handleGameFileDescriptionChange(e, index)}
                            className="w-full bg-gray-600 border border-gray-500 rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                          />
                        </div>
                      </div>

                      {/* Add name input field for external links */}
                      {gameFileItem.isExternalLink && (
                        <div>
                          <label htmlFor={`fileName-${index}`} className="block text-white font-medium mb-2">Display Name</label>
                          <input 
                            type="text" 
                            id={`fileName-${index}`}
                            placeholder="Name to display for this download"
                            value={gameFileItem.name}
                            onChange={(e) => {
                              const updatedGameFiles = [...gameFiles];
                              updatedGameFiles[index].name = e.target.value;
                              setGameFiles(updatedGameFiles);
                            }}
                            className="w-full bg-gray-600 border border-gray-500 rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                          />
                        </div>
                      )}
                      
                      {/* Update the UI text but keep the property name the same for now */}
                      <div className="space-y-2">
                        <button 
                          type="button"
                          className={`w-full flex items-center justify-center gap-2 px-4 py-3 rounded-lg font-medium transition-colors duration-200 ${
                            gameFileItem.isPremium 
                              ? 'bg-yellow-600 hover:bg-yellow-700 text-white' 
                              : 'bg-green-600 hover:bg-green-700 text-white'
                          } ${gameData.priceModel === 'free' && !gameFileItem.isPremium ? 'opacity-50 cursor-not-allowed' : ''}`}
                          onClick={() => handleTogglePremium(index)}
                          disabled={gameData.priceModel === 'free' && !gameFileItem.isPremium} // Only disable for free games with free content
                        >
                          {gameFileItem.isPremium ? (
                            <>
                              <FaLock className="text-lg" /> 
                              <span>Locked Content (Requires Purchase)</span>
                            </>
                          ) : (
                            <>
                              <FaLockOpen className="text-lg" />
                              <span>Free Content (Available to Everyone)</span>
                            </>
                          )}
                        </button>
                        
                        <div className="text-gray-400 text-sm">
                          {gameFileItem.isPremium 
                            ? "This file will only be available after purchase. Click to make it free." 
                            : gameData.priceModel === 'free'
                              ? "Free games can only have free content available to everyone."
                              : "This file will be available for free download. Click to make it premium content."}
                        </div>
                      </div>

                      {/* Web Playable Settings */}
                      <div className="space-y-4 border-t border-gray-600 pt-4">
                        <h4 className="text-white font-medium">Web Game Settings</h4>
                        
                        <div className="space-y-2">
                          <button 
                            type="button"
                            className={`w-full flex items-center justify-center gap-2 px-4 py-3 rounded-lg font-medium transition-colors duration-200 ${
                              gameFileItem.isWebPlayable 
                                ? 'bg-blue-600 hover:bg-blue-700 text-white' 
                                : 'bg-gray-600 hover:bg-gray-500 text-white'
                            }`}
                            onClick={() => handleToggleWebPlayable(index)}
                          >
                            {gameFileItem.isWebPlayable ? (
                              <>
                                <FaGamepad className="text-lg" /> 
                                <span>Web Playable (Can be played in browser)</span>
                              </>
                            ) : (
                              <>
                                <FaDownload className="text-lg" />
                                <span>Download Only (Regular file)</span>
                              </>
                            )}
                          </button>
                          
                          <div className="text-gray-400 text-sm">
                            {gameFileItem.isWebPlayable 
                              ? "This file can be played directly in the browser. Make sure it's an HTML5/WebGL game." 
                              : "This file requires download to play. Click to enable web playback."}
                          </div>
                        </div>

                        {gameFileItem.isWebPlayable && (
                          <div>
                            <label htmlFor={`webEntryPoint-${index}`} className="block text-white font-medium mb-2">Entry Point File</label>
                            <input 
                              type="text" 
                              id={`webEntryPoint-${index}`}
                              placeholder="e.g., index.html"
                              value={gameFileItem.webEntryPoint}
                              onChange={(e) => handleWebEntryPointChange(e, index)}
                              className="w-full bg-gray-600 border border-gray-500 rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                            />
                            <div className="text-gray-400 text-sm mt-1">
                              The main HTML file that starts your web game (usually index.html)
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
                
                {gameFiles.length < 5 && (
                  <button 
                    type="button"
                    className="w-full flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200"
                    onClick={handleAddGameFile}
                  >
                    <FaPlus /> Add Another File {gameFiles.length}/5
                  </button>
                )}
                
                {gameFiles.length === 0 && (
                  <div className="text-center py-8 bg-gray-700 rounded-lg border border-gray-600">
                    <FaDownload className="text-gray-400 text-4xl mx-auto mb-4" />
                    <p className="text-gray-300">No files added yet. Click &ldquo;Add Another File&rdquo; to upload your game.</p>
                  </div>
                )}
                
                <div className="text-gray-400 text-sm mt-4">
                  Maximum file size: 500MB per file. Supported formats: ZIP, RAR, 7Z
                </div>
                
                {gameData.priceModel !== 'free' && gameFiles.some(f => f.isPremium) && (
                  <div className="bg-blue-900 bg-opacity-50 border border-blue-600 rounded-lg p-4 mt-4">
                    <div className="flex items-start gap-3">
                      <FaInfoCircle className="text-blue-400 text-lg mt-0.5" />
                      <p className="text-blue-200">
                        You&apos;ve marked some files as locked content. These files will only be available to users 
                        who purchase your game. Free files will be available for download to everyone.
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>







            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <h2 className="text-2xl font-bold text-white mb-4">Web Game Configuration</h2>
              <p className="text-gray-300 mb-6">Configure if your game can be played directly in the browser</p>

              <div className="space-y-6">
                <div className="bg-gray-700 rounded-lg p-4 border border-gray-600">
                  <div className="flex items-center gap-3 mb-3">
                    <input
                      type="checkbox"
                      id="isWebGame"
                      name="isWebGame"
                      checked={gameData.isWebGame}
                      onChange={(e) => setGameData({...gameData, isWebGame: e.target.checked})}
                      className="w-4 h-4 text-orange-500 bg-gray-600 border-gray-500 rounded focus:ring-orange-500 focus:ring-2"
                    />
                    <label htmlFor="isWebGame" className="text-white font-semibold cursor-pointer">
                      This is a web game (playable in browser)
                    </label>
                  </div>
                  <p className="text-gray-400 text-sm">
                    Check this if your game can be played directly in a web browser without downloading
                  </p>
                </div>

                {gameData.isWebGame && (
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="webGameType" className="block text-white font-medium mb-2">Web Game Technology</label>
                      <select 
                        id="webGameType" 
                        name="webGameType" 
                        value={gameData.webGameType}
                        onChange={handleInputChange}
                        className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      >
                        <option value="html5">HTML5/JavaScript</option>
                        <option value="webgl">WebGL</option>
                        <option value="unity">Unity WebGL</option>
                        <option value="construct">Construct 3</option>
                        <option value="phaserjs">Phaser.js</option>
                        <option value="gamemaker">GameMaker Studio</option>
                        <option value="godot">Godot</option>
                        <option value="other">Other</option>
                      </select>
                    </div>

                    <div>
                      <label htmlFor="webGameUrl" className="block text-white font-medium mb-2">External Web Game URL (Optional)</label>
                      <input 
                        type="text" 
                        id="webGameUrl" 
                        name="webGameUrl" 
                        value={gameData.webGameUrl}
                        onChange={handleInputChange}
                        placeholder="e.g., https://yourgame.com/play or leave empty if uploading files"
                        className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                      />
                      <div className="text-gray-400 text-sm mt-1">
                        If your game is hosted elsewhere, provide the direct play URL. Otherwise, upload web-playable files above.
                      </div>
                    </div>

                    <div className="bg-gray-700 rounded-lg p-4 border border-gray-600">
                      <div className="flex items-center gap-3 mb-3">
                        <input
                          type="checkbox"
                          id="hasEmbeddedVersion"
                          name="hasEmbeddedVersion"
                          checked={gameData.hasEmbeddedVersion}
                          onChange={(e) => setGameData({...gameData, hasEmbeddedVersion: e.target.checked})}
                          className="w-4 h-4 text-orange-500 bg-gray-600 border-gray-500 rounded focus:ring-orange-500 focus:ring-2"
                        />
                        <label htmlFor="hasEmbeddedVersion" className="text-white font-semibold cursor-pointer">
                          Game has embedded version for direct play
                        </label>
                      </div>
                      <p className="text-gray-400 text-sm">
                        Check this if you&apos;ve uploaded files that can be played directly on the game page
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
              <h2 className="text-2xl font-bold text-white mb-4">Release Information</h2>
              
              <div>
                <label htmlFor="releaseDate" className="block text-white font-medium mb-2">Release Date</label>
                <input 
                  type="date" 
                  id="releaseDate" 
                  name="releaseDate" 
                  value={gameData.releaseDate}
                  onChange={handleInputChange}
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                />
                <div className="text-gray-400 text-sm mt-1">
                  When was your game first released?
                </div>
              </div>
            </div>



            {errors.submit && <div className="bg-red-900 bg-opacity-50 border border-red-600 text-red-200 p-4 rounded-lg">{errors.submit}</div>}

            <div className="flex justify-center pt-6">
              <button 
                type="submit" 
                className="bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 disabled:from-gray-600 disabled:to-gray-600 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-200 disabled:cursor-not-allowed"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Uploading...' : 'Upload Game'}
              </button>
            </div>
            
          </form>
        )}
      </div>

      {/* Game Preview Panel */}
      <GamePreviewPanel
        isOpen={previewOpen}
        onClose={closePreview}
        gameData={gameData}
        gameFiles={gameFiles}
        coverImagePreview={null}
        cardImagePreview={cardImagePreview}
        gifAnimationPreview={gifAnimationPreview}
        screenshotPreviews={[]}
        gameCardPreview={gameCardPreview}
      />

      {/* Overlay for mobile view */}
      {previewOpen && window.innerWidth <= 992 && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-30" onClick={closePreview}></div>
      )}
    </div>
  );
};

export default UploadGamePage;
