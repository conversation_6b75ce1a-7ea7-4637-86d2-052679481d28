import axios from 'axios';

import { API_URL } from '../config/env.js';

// Create an instance of axios with baseURL
const uploadApi = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'multipart/form-data' // For file uploads
  }
});

// Add auth token to requests
uploadApi.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Modified to handle external links in addition to uploads
export const uploadGame = async (
  gameData,
  gameFiles, // Now can include externalUrl property
  coverImage,
  cardImage,
  gifAnimation,
  screenshots
) => {
  try {
    // Create FormData object to handle file uploads
    const formData = new FormData();
    
    // Add game metadata as JSON string
    formData.append('gameData', JSON.stringify(gameData));
    
    // Handle game files (both uploads and external links)
    if (gameFiles && gameFiles.length > 0) {
      // Create a metadata array for the game files
      const gameFilesMetadata = gameFiles.map((fileItem, index) => {
        return {
          index: index,
          name: fileItem.name || '',
          description: fileItem.description || '',
          fileType: fileItem.fileType || 'game',
          isPremium: fileItem.isPremium || false,
          isExternalLink: fileItem.isExternalLink || false,
          externalUrl: fileItem.externalUrl || '',
          isWebPlayable: fileItem.isWebPlayable || false,
          webEntryPoint: fileItem.webEntryPoint || 'index.html'
        };
      });
      
      // Add metadata as JSON string
      formData.append('gameFilesMetadata', JSON.stringify(gameFilesMetadata));
      
      // Only add actual files for non-external links
      gameFiles.forEach((fileItem, index) => {
        if (fileItem.file && !fileItem.isExternalLink) {
          formData.append(`gameFile_${index}`, fileItem.file);
        }
      });
    }
    
    // Add other files
    if (coverImage) {
      formData.append('coverImage', coverImage);
    }
    
    if (cardImage) {
      formData.append('cardImage', cardImage);
    }
    
    if (gifAnimation) {
      formData.append('gifAnimation', gifAnimation);
    }
    
    // Add screenshots if any
    if (screenshots && screenshots.length > 0) {
      screenshots.forEach((file, index) => {
        formData.append(`screenshot_${index}`, file);
      });
    }
    
    // Make API call to upload the game
    const response = await axios.post(`${API_URL}/upload/game`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        Authorization: `Bearer ${localStorage.getItem('token')}`
      },
      withCredentials: true
    });
    
    return response.data;
  } catch (error) {
    console.error('Error uploading game:', error);
    throw error.response?.data || { message: 'Network error during upload' };
  }
};
